# YummyTummy Django Environment Variables
# Copy this file to .env and fill in your values

# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=False
DJANGO_LOG_LEVEL=INFO

# Allowed Hosts - Using wildcard for simplified deployment
ALLOWED_HOSTS=*

# CSRF Trusted Origins (comma-separated) - Must include https://
# Examples:
# For Render.com only: CSRF_TRUSTED_ORIGINS=https://*.onrender.com
# For custom domain: CSRF_TRUSTED_ORIGINS=https://*.onrender.com,https://yummytummy.com,https://www.yummytummy.com
CSRF_TRUSTED_ORIGINS=https://*.onrender.com,https://livegreat.co.ke,https://www.livegreat.co.ke

# Database Configuration - Neon PostgreSQL
# For production: Set DATABASE_URL to your Neon PostgreSQL connection string
# For development: Use the same Neon database or leave empty for SQLite
# Example Neon URL: DATABASE_URL=************************************************************
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# Security Settings (for production)
SECURE_SSL_REDIRECT=True
SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https

# Email Settings (for order confirmations and user notifications)
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend

# Gmail Configuration (recommended)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=roqu frlt wvof rqxk
DEFAULT_FROM_EMAIL=YummyTummy Store <<EMAIL>>

# Alternative Email Providers:
# Outlook: EMAIL_HOST=smtp-mail.outlook.com, EMAIL_PORT=587
# Yahoo: EMAIL_HOST=smtp.mail.yahoo.com, EMAIL_PORT=587
# Custom SMTP: Use your provider's settings

# Site URL Configuration (for M-Pesa callbacks and production)
SITE_URL=https://livegreat.co.ke

# M-Pesa Configuration (for mobile money payments)
MPESA_BUSINESS_SHORT_CODE=6319470
MPESA_PASSKEY=your_mpesa_passkey
MPESA_CONSUMER_KEY=your_mpesa_consumer_key
MPESA_CONSUMER_SECRET=your_mpesa_consumer_secret
MPESA_ENVIRONMENT=sandbox
