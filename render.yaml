services:
  - type: web
    name: yummytummy-store
    env: python
    plan: free
    branch: live
    buildCommand: "./build.sh"
    startCommand: "gunicorn yummytummy_project.wsgi:application"
    envVars:
      - key: DATABASE_URL
        value: "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
      - key: SECRET_KEY
        generateValue: true
      - key: WEB_CONCURRENCY
        value: 4
      - key: DJANGO_SETTINGS_MODULE
        value: yummytummy_project.settings
      - key: DEBUG
        value: "False"
      - key: ALLOWED_HOSTS
        value: "*"
      - key: CSRF_TRUSTED_ORIGINS
        value: "https://*.onrender.com"
      - key: SECURE_SSL_REDIRECT
        value: "True"
      - key: SECURE_PROXY_SSL_HEADER
        value: "HTTP_X_FORWARDED_PROTO,https"
      - key: SECURE_HSTS_SECONDS
        value: "31536000"
      - key: SESSION_COOKIE_SECURE
        value: "True"
      - key: CSRF_COOKIE_SECURE
        value: "True"
      # Email Configuration
      - key: EMAIL_BACKEND
        value: "django.core.mail.backends.smtp.EmailBackend"
      - key: EMAIL_HOST
        value: "smtp.gmail.com"
      - key: EMAIL_PORT
        value: "587"
      - key: EMAIL_USE_TLS
        value: "True"
      - key: EMAIL_HOST_USER
        value: "<EMAIL>"
      - key: EMAIL_HOST_PASSWORD
        value: "roqu frlt wvof rqxk"
      - key: DEFAULT_FROM_EMAIL
        value: "YummyTummy Goodies"
      # Uploadcare Configuration
      - key: UPLOADCARE_PUBLIC_KEY
        value: "********************"
      - key: UPLOADCARE_SECRET_KEY
        value: "********************"
      # M-Pesa Configuration (Live Safaricom Credentials)
      - key: MPESA_BUSINESS_SHORT_CODE
        value: "6319470"
      - key: MPESA_PASSKEY
        value: "f473271a17488fd9a1230c2e43f6fe63db04eabc8bc7db8d1e21e4fe753f598d"
      - key: MPESA_CONSUMER_KEY
        value: "p2D6eI01gcYtvwHgrh7UVsX61sFaAmKA4hZDZaHI3KBN0Xv4"
      - key: MPESA_CONSUMER_SECRET
        value: "MQKc16J58WljEleReHaRAXzXSv6nmyxWCYqxAKzvE3NNUIpDYk94LJzQwTu1pGJn"
      - key: MPESA_ENVIRONMENT
        value: "production"
