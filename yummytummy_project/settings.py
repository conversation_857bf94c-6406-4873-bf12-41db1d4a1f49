"""
Django settings for yummytummy_project project.

Generated by 'django-admin startproject' using Django 4.2.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
import dj_database_url
from pathlib import Path
from decouple import config

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config('SECRET_KEY', default='django-insecure-*vzt0ewazs4fk@4n^_jaq7wqfc7b=qt*1ykc6mb=i^5u5)zmfj')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config('DEBUG', default=True, cast=bool)

# ALLOWED_HOSTS configuration for development and production
# Using specific domains for security instead of wildcard
ALLOWED_HOSTS_LIST = [
    'localhost', '127.0.0.1',  # Development
    'testserver',  # Django test client
    'livegreat.co.ke',  # Primary domain (apex domain)
    'www.livegreat.co.ke',  # WWW subdomain (for compatibility)
]

# Add Render.com domains dynamically
RENDER_DOMAINS = [
    '.onrender.com',
    'yummytummy-store.onrender.com',  # Specific Render app
]

ALLOWED_HOSTS = ALLOWED_HOSTS_LIST + RENDER_DOMAINS

# Production security settings
if not DEBUG:
    # SSL/HTTPS Security
    SECURE_SSL_REDIRECT = config('SECURE_SSL_REDIRECT', default=True, cast=bool)
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

    # HTTP Strict Transport Security (HSTS)
    SECURE_HSTS_SECONDS = config('SECURE_HSTS_SECONDS', default=31536000, cast=int)  # 1 year
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True

    # Additional Security Headers
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_BROWSER_XSS_FILTER = True
    X_FRAME_OPTIONS = 'DENY'

    # Secure Cookies (HTTPS only)
    SESSION_COOKIE_SECURE = config('SESSION_COOKIE_SECURE', default=True, cast=bool)
    CSRF_COOKIE_SECURE = config('CSRF_COOKIE_SECURE', default=True, cast=bool)

    # CSRF settings for production
    # Prioritize www subdomain in trusted origins for consistency
    default_csrf_origins = 'https://www.livegreat.co.ke,https://livegreat.co.ke,https://*.onrender.com'
    CSRF_TRUSTED_ORIGINS = config('CSRF_TRUSTED_ORIGINS', default=default_csrf_origins, cast=lambda v: [s.strip() for s in v.split(',') if s.strip()])


# Application definition

INSTALLED_APPS = [
    # Custom apps (must be before Django auth to override templates)
    'yummytummy_store.apps.YummytummyStoreConfig',

    # Django default apps
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.humanize',  # For currency formatting with commas

    # Third-party apps
    'crispy_forms',
    'crispy_bootstrap4',
    'pyuploadcare.dj',
]

# Crispy Forms
CRISPY_TEMPLATE_PACK = 'bootstrap4'
CRISPY_ALLOWED_TEMPLATE_PACKS = 'bootstrap4'

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',  # For static file serving in production
    # 'yummytummy_store.middleware.WWWRedirectMiddleware',  # REMOVED: Custom domain redirection (causing redirect loop)
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'yummytummy_store.middleware.SecurityHeadersMiddleware',  # Custom security headers
]

ROOT_URLCONF = 'yummytummy_project.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            BASE_DIR / 'templates',
        ],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'yummytummy_store.context_processors.cart_processor',
            ],
        },
    },
]

WSGI_APPLICATION = 'yummytummy_project.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

# Use PostgreSQL in production, SQLite in development
DATABASE_URL = config('DATABASE_URL', default=None)

if DATABASE_URL:
    # Production database (Neon PostgreSQL)
    DATABASES = {
        'default': dj_database_url.parse(DATABASE_URL)
    }
else:
    # Development database (SQLite)
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

# Kenya timezone (East Africa Time - UTC+3)
TIME_ZONE = 'Africa/Nairobi'

USE_I18N = True

# Keep timezone support enabled for proper timezone handling
USE_TZ = True


# Authentication Settings
LOGIN_URL = '/accounts/login/'
LOGIN_REDIRECT_URL = '/account/dashboard/'
LOGOUT_REDIRECT_URL = '/'


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = '/static/'
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# WhiteNoise configuration for production static file serving
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Session settings
SESSION_ENGINE = 'django.contrib.sessions.backends.db'  # Store sessions in the database
SESSION_COOKIE_AGE = 86400 * 7  # 7 days session cookie age
SESSION_SAVE_EVERY_REQUEST = True  # Save the session on every request

# YummyTummy branding colors for admin (kept for reference only)
# Primary color: #593500 (brown)
# Secondary color: #ffffff (white)
# Accent color: #f5f2ed (cream)
# Highlight color: #ffc107 (yellow)

# Logging configuration for production
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': config('DJANGO_LOG_LEVEL', default='INFO'),
            'propagate': False,
        },
        'yummytummy_store.mpesa_service': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# Email Configuration
EMAIL_BACKEND = config('EMAIL_BACKEND', default='django.core.mail.backends.smtp.EmailBackend')
EMAIL_HOST = config('EMAIL_HOST', default='smtp.gmail.com')
EMAIL_PORT = config('EMAIL_PORT', default=587, cast=int)
EMAIL_USE_TLS = config('EMAIL_USE_TLS', default=True, cast=bool)
EMAIL_HOST_USER = config('EMAIL_HOST_USER', default='')
EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD', default='')
DEFAULT_FROM_EMAIL = config('DEFAULT_FROM_EMAIL', default='YummyTummy Store <<EMAIL>>')

# Email timeout settings
EMAIL_TIMEOUT = 30

# SSL settings for email (for development - disable SSL verification)
import ssl
EMAIL_USE_SSL = False
EMAIL_SSL_CERTFILE = None
EMAIL_SSL_KEYFILE = None

# For development, you might want to use console backend to avoid SSL issues
if DEBUG:
    # Uncomment the line below to use console email backend for development
    # EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
    pass

# Uploadcare settings for image management
UPLOADCARE = {
    'pub_key': config('UPLOADCARE_PUBLIC_KEY', default='********************'),
    'secret': config('UPLOADCARE_SECRET_KEY', default='********************'),
    'widget_version': '3.x',
    'widget_build': 'min',
    'cdn_base': 'https://ucarecdn.com/',
    'use_hosted_assets': True,
    'validators': {
        'image': {
            'allowed_extensions': ['jpeg', 'jpg', 'png', 'webp'],
            'max_size': 10 * 1024 * 1024,  # 10MB
        },
    },
}

# Site URL Configuration for M-Pesa Callbacks
# This is the domain that Safaricom will send callbacks to
SITE_URL = config('SITE_URL', default='https://livegreat.co.ke')

# M-Pesa Configuration
MPESA_BUSINESS_SHORT_CODE = config('MPESA_BUSINESS_SHORT_CODE', default='6319470')
MPESA_PASSKEY = config('MPESA_PASSKEY', default='f473271a17488fd9a1230c2e43f6fe63db04eabc8bc7db8d1e21e4fe753f598d')
MPESA_CONSUMER_KEY = config('MPESA_CONSUMER_KEY', default='p2D6eI01gcYtvwHgrh7UVsX61sFaAmKA4hZDZaHI3KBN0Xv4')
MPESA_CONSUMER_SECRET = config('MPESA_CONSUMER_SECRET', default='MQKc16J58WljEleReHaRAXzXSv6nmyxWCYqxAKzvE3NNUIpDYk94LJzQwTu1pGJn')

# M-Pesa Transaction Type Configuration
# CustomerPayBillOnline: For Paybill Numbers
# CustomerBuyGoodsOnline: For Till Numbers
# Based on ResultCode 2028 error analysis, shortcode 6319470 appears to be a Till Number
MPESA_TRANSACTION_TYPE = config('MPESA_TRANSACTION_TYPE', default='CustomerBuyGoodsOnline')

# M-Pesa API URLs (Sandbox for testing, Production for live)
MPESA_ENVIRONMENT = config('MPESA_ENVIRONMENT', default='production')  # 'sandbox' or 'production'
MPESA_BASE_URL = 'https://sandbox.safaricom.co.ke' if MPESA_ENVIRONMENT == 'sandbox' else 'https://api.safaricom.co.ke'

# M-Pesa Callback URL Configuration
# Safaricom requires HTTPS and will only send callbacks to the registered domain
MPESA_CALLBACK_URL = f"{SITE_URL}/mpesa/callback/"
