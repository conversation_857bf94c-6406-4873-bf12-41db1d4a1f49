{% extends "yummytummy_store/base.html" %}
{% load static %}
{% load humanize %}
{% load image_filters %}

{% block content %}
<!-- Hero Slider -->
<section class="hero-slider">
    <div class="slider-container" style="background-image: url('{% static 'yummytummy_store/img/homebg.webp' %}'); background-size: 75%; background-position: center; background-repeat: no-repeat;">
        <div class="slide active">
            <div class="left-content">
                <h2>BEST<br>SELLERS</h2>
                <div class="product-variants">
                    <div class="variant">
                        <img src="{% static 'yummytummy_store/img/slide-extra.png' %}" alt="Extra ground">
                        <div class="variant-text">
                            <span>EXTRA</span>
                            <span>GROUND</span>
                        </div>
                    </div>
                    <div class="variant">
                        <img src="{% static 'yummytummy_store/img/slide-mega.png' %}" alt="Mega crunch">
                        <div class="variant-text">
                            <span>MEGA</span>
                            <span>CRUNCH</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="center-content">
                <div class="features-left">
                    <div class="feature">
                        <span class="percentage">100%</span>
                        <p>PEANUTS</p>
                    </div>
                    <div class="feature">
                        <span class="percentage">0%</span>
                        <p>PRESERVATIVES</p>
                    </div>
                </div>
                <div class="product-image">
                    {% if highlighted_product %}
                        <img src="{{ highlighted_product|product_image_list }}" alt="{{ highlighted_product.name }}" loading="lazy">
                    {% else %}
                        <img src="{% static 'yummytummy_store/img/peanutmockup.webp' %}" alt="YummyTummy peanut butter" loading="lazy">
                    {% endif %}
                </div>
            </div>

            <div class="right-content">
                {% if highlighted_product %}
                <div class="product-preview highlighted-product expandable-card" data-product-id="{{ highlighted_product.id }}">
                    <div class="squirrel-icon">

                    </div>
                    {% if highlighted_product.is_featured and highlighted_product.feature_type %}
                    <div class="feature-badge {{ highlighted_product.feature_type }}">
                        {{ highlighted_product.get_feature_type_display }}
                    </div>
                    {% endif %}

                    <!-- Compact View (Default) -->
                    <div class="card-compact hero-compact">
                        <h3>{{ highlighted_product.name|upper }}{% if highlighted_product.size %}<br>{{ highlighted_product.size }}{% endif %}</h3>

                        <!-- Price Summary -->
                        <div class="product-price-summary hero-price">
                            {% if highlighted_product.variants.exists %}
                            <div class="price-range-compact hero-range">
                                <span class="from-label">From</span>
                                <span class="currency">KSh</span>
                                <span class="amount">{{ highlighted_product.price|floatformat:"0"|intcomma }}</span>
                                <span class="range-indicator">+</span>
                            </div>
                            {% else %}
                            <div class="price">
                                <span class="currency">KSh</span>
                                <span class="amount">{{ highlighted_product.price|floatformat:"0"|intcomma }}</span>
                                <span class="decimal">.{{ highlighted_product.price|floatformat:"2"|slice:"3:" }}</span>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Expand/Collapse Button or Direct Add to Cart -->
                        {% if highlighted_product.variants.exists %}
                        <button type="button" class="expand-btn hero-expand" data-expanded="false">
                            <span class="expand-text">View Sizes</span>
                            <span class="collapse-text">Close</span>
                            <svg class="expand-icon" width="16" height="16" viewBox="0 0 16 16">
                                <path d="M8 12l-4-4h8l-4 4z" fill="currentColor"/>
                            </svg>
                        </button>
                        {% else %}
                        <form action="{% url 'yummytummy_store:cart_add' highlighted_product.id %}" method="post" class="hero-quick-add-form">
                            {% csrf_token %}
                            <div class="quantity-selector">
                                <button type="button" class="minus">-</button>
                                {% if cart_product_form %}
                                    {{ cart_product_form.quantity }}
                                {% else %}
                                    <input type="number" name="quantity" value="1" min="1" class="form-control">
                                {% endif %}
                                <button type="button" class="plus">+</button>
                            </div>
                            <input type="hidden" name="update" value="False">
                            <button type="submit" class="add-to-cart">ADD TO CART</button>
                        </form>
                        {% endif %}
                    </div>

                    <!-- Expanded View (Hidden by default) -->
                    {% if highlighted_product.variants.exists %}
                    <div class="card-expanded hero-expanded" style="display: none;">
                        <div class="variants-header">
                            <h4>Choose Your Size</h4>
                            <p class="variants-subtitle">Select the perfect size for your needs</p>
                        </div>

                        <div class="variants-grid hero-variants">
                            <!-- Base Product Option -->
                            {% if highlighted_product.size %}
                            <div class="variant-option" data-variant-id="base" data-price="{{ highlighted_product.price }}">
                                <div class="variant-card">
                                    <div class="variant-size">{{ highlighted_product.size }}</div>
                                    <div class="variant-price">
                                        <span class="currency">KSh</span>
                                        <span class="amount">{{ highlighted_product.price|floatformat:"0"|intcomma }}</span>
                                    </div>
                                    <div class="variant-description">Standard size</div>
                                    <form action="{% url 'yummytummy_store:cart_add' highlighted_product.id %}" method="post" class="variant-form">
                                        {% csrf_token %}
                                        <div class="quantity-selector mini">
                                            <button type="button" class="minus">-</button>
                                            <input type="number" name="quantity" value="1" min="1" class="form-control">
                                            <button type="button" class="plus">+</button>
                                        </div>
                                        <input type="hidden" name="update" value="False">
                                        <input type="hidden" name="selected_variant" value="base">
                                        <button type="submit" class="add-to-cart variant">Add to Cart</button>
                                    </form>
                                </div>
                            </div>
                            {% endif %}

                            <!-- Product Variants -->
                            {% for variant in highlighted_product.variants.all %}
                            <div class="variant-option" data-variant-id="{{ variant.id }}" data-price="{{ highlighted_product.price|add:variant.additional_price }}">
                                <div class="variant-card">
                                    <div class="variant-size">{{ variant.name }}</div>
                                    <div class="variant-price">
                                        <span class="currency">KSh</span>
                                        <span class="amount">{{ highlighted_product.price|add:variant.additional_price|floatformat:"0"|intcomma }}</span>
                                    </div>
                                    {% if variant.additional_price > 0 %}
                                    <div class="variant-description">+KSh {{ variant.additional_price|floatformat:"0"|intcomma }} extra</div>
                                    {% else %}
                                    <div class="variant-description">Same price</div>
                                    {% endif %}
                                    <form action="{% url 'yummytummy_store:cart_add' highlighted_product.id %}" method="post" class="variant-form">
                                        {% csrf_token %}
                                        <div class="quantity-selector mini">
                                            <button type="button" class="minus">-</button>
                                            <input type="number" name="quantity" value="1" min="1" class="form-control">
                                            <button type="button" class="plus">+</button>
                                        </div>
                                        <input type="hidden" name="update" value="False">
                                        <input type="hidden" name="selected_variant" value="{{ variant.id }}">
                                        <button type="submit" class="add-to-cart variant">Add to Cart</button>
                                    </form>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="about-section">
    <h2 class="section-title">peanut butter</h2>
    <p class="section-subtitle">DISCOVER OUR STORY AND PROCESS</p>
    <div class="about-content">
        <p>Our adventure with YummyTummy began when we started searching for the perfect peanut butter for our loved ones. Because it was difficult to find products of the highest quality, we decided to take matters into our own hands. And so our first <strong>homemade butter</strong> was created.</p>
    </div>
</section>

<!-- Process Section -->
<section class="process-section">
    <div class="container">
        <div class="process-steps">
            <div class="process-step">
                <img src="{% static 'yummytummy_store/img/squirrel.png' %}" alt="Selection and sorting of nuts" class="process-image">
                <div class="step-number">
                    <img src="{% static 'yummytummy_store/img/number-1.svg' %}" alt="1">
                </div>
                <h3>SELECTION<br>AND SORTING<br>OF NUTS</h3>
                <p>The nuts we use come from many different corners of the world. We specially import them from India, USA, Bolivia, Argentina, Brazil, Peru, Indonesia and Australia.</p>
            </div>
            <div class="process-step">
                <img src="{% static 'yummytummy_store/img/peanutai.png' %}" alt="Grinding process" class="process-image">
                <div class="step-number">
                    <img src="{% static 'yummytummy_store/img/number-2.svg' %}" alt="2">
                </div>
                <h3>1 GRINDING<br>=50 BUTTERS</h3>
                <p>In our nut factory, unique nut spreads are created. Our highly specialized "Butter Masters" oversee every stage of their production.</p>
            </div>
            <div class="process-step">
                <img src="{% static 'yummytummy_store/img/peanutmockup.webp' %}" alt="Shop for products" class="process-image">
                <div class="step-number">
                    <img src="{% static 'yummytummy_store/img/number-3.svg' %}" alt="3">
                </div>
                <h3>SHOP</h3>
                <p>Each butter has a unique taste, aroma and texture, which depends on both the level of roasting of the nuts and the degree of their grinding.</p>
            </div>
        </div>
        <div class="process-cta">
            <a href="{% url 'yummytummy_store:product_list' %}" class="btn-primary">to the shop</a>
        </div>
    </div>
</section>

<!-- Product Showcase -->
<section class="product-showcase">
    <div class="container">
        <h2 class="showcase-title">PROBABLY</h2>
        <h3 class="showcase-subtitle">THE BEST BUTTER IN THE WORLD</h3>
        <div class="product-slider">
            {% for product in featured_products %}
            <div class="product-card expandable-card" data-product-id="{{ product.id }}">
                <!-- Compact View (Default) -->
                <div class="card-compact">
                    <div class="product-image">
                        <img src="{{ product|product_image_list }}" alt="{{ product.name }}" loading="lazy">
                    </div>
                    <h3 class="product-title">
                        <a href="{{ product.get_absolute_url }}">{{ product.name }}</a>
                    </h3>
                    <div class="product-price-summary">
                        {% if product.variants.exists %}
                        <div class="price-range-compact">
                            <span class="from-label">From</span>
                            <span class="currency">KSh</span>
                            <span class="amount">{{ product.price|floatformat:"0"|intcomma }}</span>
                            <span class="range-indicator">+</span>
                        </div>
                        {% else %}
                        <div class="single-price">
                            <span class="currency">KSh</span>
                            <span class="amount">{{ product.price|floatformat:"0"|intcomma }}</span>
                            <span class="decimal">.{{ product.price|floatformat:"2"|slice:"3:" }}</span>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Expand/Collapse Button -->
                    {% if product.variants.exists %}
                    <button type="button" class="expand-btn" data-expanded="false">
                        <span class="expand-text">View Sizes</span>
                        <span class="collapse-text">Close</span>
                        <svg class="expand-icon" width="16" height="16" viewBox="0 0 16 16">
                            <path d="M8 12l-4-4h8l-4 4z" fill="currentColor"/>
                        </svg>
                    </button>
                    {% else %}
                    <form action="{% url 'yummytummy_store:cart_add' product.id %}" method="post" class="quick-add-form">
                        {% csrf_token %}
                        <input type="hidden" name="quantity" value="1">
                        <input type="hidden" name="update" value="False">
                        <button type="submit" class="add-to-cart compact">Quick Add</button>
                    </form>
                    {% endif %}
                </div>

                <!-- Expanded View (Hidden by default) -->
                {% if product.variants.exists %}
                <div class="card-expanded" style="display: none;">
                    <div class="variants-header">
                        <h4>Choose Your Size</h4>
                        <p class="variants-subtitle">Select the perfect size for your needs</p>
                    </div>

                    <div class="variants-grid">
                        <!-- Base Product Option -->
                        {% if product.size %}
                        <div class="variant-option" data-variant-id="base" data-price="{{ product.price }}">
                            <div class="variant-card">
                                <div class="variant-size">{{ product.size }}</div>
                                <div class="variant-price">
                                    <span class="currency">KSh</span>
                                    <span class="amount">{{ product.price|floatformat:"0"|intcomma }}</span>
                                </div>
                                <div class="variant-description">Standard size</div>
                                <form action="{% url 'yummytummy_store:cart_add' product.id %}" method="post" class="variant-form">
                                    {% csrf_token %}
                                    <div class="quantity-selector mini">
                                        <button type="button" class="minus">-</button>
                                        <input type="number" name="quantity" value="1" min="1" class="form-control">
                                        <button type="button" class="plus">+</button>
                                    </div>
                                    <input type="hidden" name="update" value="False">
                                    <input type="hidden" name="selected_variant" value="base">
                                    <button type="submit" class="add-to-cart variant">Add to Cart</button>
                                </form>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Product Variants -->
                        {% for variant in product.variants.all %}
                        <div class="variant-option" data-variant-id="{{ variant.id }}" data-price="{{ product.price|add:variant.additional_price }}">
                            <div class="variant-card">
                                <div class="variant-size">{{ variant.name }}</div>
                                <div class="variant-price">
                                    <span class="currency">KSh</span>
                                    <span class="amount">{{ product.price|add:variant.additional_price|floatformat:"0"|intcomma }}</span>
                                </div>
                                {% if variant.additional_price > 0 %}
                                <div class="variant-description">+KSh {{ variant.additional_price|floatformat:"0"|intcomma }} extra</div>
                                {% else %}
                                <div class="variant-description">Same price</div>
                                {% endif %}
                                <form action="{% url 'yummytummy_store:cart_add' product.id %}" method="post" class="variant-form">
                                    {% csrf_token %}
                                    <div class="quantity-selector mini">
                                        <button type="button" class="minus">-</button>
                                        <input type="number" name="quantity" value="1" min="1" class="form-control">
                                        <button type="button" class="plus">+</button>
                                    </div>
                                    <input type="hidden" name="update" value="False">
                                    <input type="hidden" name="selected_variant" value="{{ variant.id }}">
                                    <button type="submit" class="add-to-cart variant">Add to Cart</button>
                                </form>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
            {% empty %}
            <p>No products available at the moment.</p>
            {% endfor %}
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="{% static 'yummytummy_store/js/expandable-cards.js' %}"></script>
{% endblock %}
