{% extends "yummytummy_store/base.html" %}
{% load static %}
{% load humanize %}
{% load image_filters %}

{% block title %}
{% if category %}{{ category.name }}{% else %}Products{% endif %} | YummyTummy Shop
{% endblock %}

{% block content %}
<section class="product-list-section">
    <div class="container">
        <div class="product-list-header">
            <h1 class="section-title">
                {% if category %}
                    {{ category.name }}
                {% else %}
                    All Products
                {% endif %}
            </h1>

            <div class="enhanced-search-bar">
                <form method="get" class="search-form">
                    {{ form.query }}
                    <button type="submit" class="search-button" aria-label="Search products">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>

        <div class="enhanced-category-nav">
            <div class="category-scroll">
                <ul>
                    <li {% if not category %}class="active"{% endif %}>
                        <a href="{% url 'yummytummy_store:product_list' %}" class="category-link">
                            <span class="category-text">All Products</span>
                        </a>
                    </li>
                    {% for c in categories %}
                    <li {% if category.id == c.id %}class="active"{% endif %}>
                        <a href="{{ c.get_absolute_url }}" class="category-link">
                            <span class="category-text">{{ c.name }}</span>
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>

        <div class="enhanced-product-grid">
            {% for product in products %}
            <div class="enhanced-product-card">
                {% if product.is_featured %}
                <div class="product-badge {{ product.feature_type|default:'featured' }}">
                    {{ product.get_feature_type_display|default:"Featured" }}
                </div>
                {% endif %}
                <div class="product-image-container">
                    <a href="{{ product.get_absolute_url }}" class="product-image-link">
                        <img src="{{ product|product_image_list }}" alt="{{ product.name }}" class="product-img" loading="lazy">
                        <div class="image-overlay">
                            <span class="view-details">View Details</span>
                        </div>
                    </a>
                </div>
                <div class="product-info">
                    <h3 class="product-title">
                        <a href="{{ product.get_absolute_url }}">{{ product.name }}</a>
                    </h3>
                    {% if product.size %}
                    <div class="product-size">{{ product.size }}</div>
                    {% endif %}

                    <!-- Price Display -->
                    <div class="product-price">
                        {% if product.variants.exists %}
                        <div class="price-range">
                            <span class="from-label">From</span>
                            <span class="currency">KSh</span>
                            <span class="amount">{{ product.price|floatformat:"0"|intcomma }}</span>
                            <span class="decimal">.{{ product.price|floatformat:"2"|slice:"3:" }}</span>
                        </div>
                        {% else %}
                        <span class="currency">KSh</span>
                        <span class="amount">{{ product.price|floatformat:"0"|intcomma }}</span>
                        <span class="decimal">.{{ product.price|floatformat:"2"|slice:"3:" }}</span>
                        {% endif %}
                    </div>

                    <!-- Variant Selection and Cart Form -->
                    <div class="product-list-cart-section" data-product-id="{{ product.id }}">
                        {% if product.variants.exists %}
                        <!-- Expandable Variant Selection -->
                        <div class="variant-toggle-section">
                            <button type="button" class="variant-toggle-btn" data-product-id="{{ product.id }}">
                                <span class="toggle-text">View Sizes</span>
                                <i class="fas fa-chevron-down toggle-icon"></i>
                            </button>
                        </div>

                        <!-- Expandable Variant Options -->
                        <div class="variant-options-container" id="variants-{{ product.id }}" style="display: none;">
                            <div class="variant-options">
                                <!-- Base Product Option -->
                                <div class="variant-option" data-variant="base" data-price="{{ product.price }}">
                                    <div class="variant-info">
                                        <span class="variant-name">Standard</span>
                                        <span class="variant-price">KSh {{ product.price|floatformat:"2"|intcomma }}</span>
                                    </div>
                                    <form action="{% url 'yummytummy_store:cart_add' product.id %}" method="post" class="variant-cart-form">
                                        {% csrf_token %}
                                        <div class="enhanced-quantity-selector">
                                            <button type="button" class="quantity-btn minus" aria-label="Decrease quantity">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <input type="text" name="quantity" value="1" class="quantity-input" aria-label="Product quantity">
                                            <button type="button" class="quantity-btn plus" aria-label="Increase quantity">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                        <input type="hidden" name="update" value="False">
                                        <input type="hidden" name="selected_variant" value="base">
                                        <button type="submit" class="variant-add-to-cart">
                                            <i class="fas fa-shopping-cart"></i> Add to cart
                                        </button>
                                    </form>
                                </div>

                                <!-- Variant Options -->
                                {% for variant in product.variants.all %}
                                <div class="variant-option" data-variant="{{ variant.id }}" data-price="{{ variant.calculated_price }}">
                                    <div class="variant-info">
                                        <span class="variant-name">{{ variant.name }}</span>
                                        <span class="variant-price">KSh {{ variant.calculated_price|floatformat:"2"|intcomma }}</span>
                                    </div>
                                    <form action="{% url 'yummytummy_store:cart_add' product.id %}" method="post" class="variant-cart-form">
                                        {% csrf_token %}
                                        <div class="enhanced-quantity-selector">
                                            <button type="button" class="quantity-btn minus" aria-label="Decrease quantity">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <input type="text" name="quantity" value="1" class="quantity-input" aria-label="Product quantity">
                                            <button type="button" class="quantity-btn plus" aria-label="Increase quantity">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                        <input type="hidden" name="update" value="False">
                                        <input type="hidden" name="selected_variant" value="{{ variant.id }}">
                                        <button type="submit" class="variant-add-to-cart">
                                            <i class="fas fa-shopping-cart"></i> Add to cart
                                        </button>
                                    </form>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% else %}
                        <!-- Simple Cart Form for Products Without Variants -->
                        <form action="{% url 'yummytummy_store:cart_add' product.id %}" method="post" class="cart-form">
                            {% csrf_token %}
                            <div class="enhanced-quantity-selector">
                                <button type="button" class="quantity-btn minus" aria-label="Decrease quantity">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="text" name="quantity" value="1" class="quantity-input" aria-label="Product quantity">
                                <button type="button" class="quantity-btn plus" aria-label="Increase quantity">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <input type="hidden" name="update" value="False">
                            <input type="hidden" name="selected_variant" value="base">
                            <button type="submit" class="enhanced-add-to-cart">
                                <i class="fas fa-shopping-cart"></i> Add to cart
                            </button>
                        </form>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="empty-products-container">
                <div class="empty-products-icon">
                    <i class="fas fa-box-open fa-4x"></i>
                </div>
                <h3 class="empty-products-title">No products found</h3>
                <p class="empty-products-message">We couldn't find any products matching your criteria.</p>
                <a href="{% url 'yummytummy_store:product_list' %}" class="return-to-shop">
                    <i class="fas fa-arrow-left"></i> Return to all products
                </a>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
    /* Enhanced Product List Styles */
    .product-list-section {
        padding: 40px 0;
    }

    .product-list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        flex-wrap: wrap;
        gap: 20px;
    }

    /* Enhanced Category Navigation */
    .enhanced-category-nav {
        margin-bottom: 40px;
        position: relative;
    }

    .category-scroll {
        overflow-x: auto;
        padding-bottom: 5px;
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .category-scroll::-webkit-scrollbar {
        display: none;
    }

    .enhanced-category-nav ul {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        gap: 10px;
    }

    .enhanced-category-nav li {
        margin: 0;
    }

    .category-link {
        display: block;
        padding: 10px 20px;
        border-radius: 30px;
        background-color: var(--light-gray);
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        white-space: nowrap;
    }

    .enhanced-category-nav li.active .category-link {
        background-color: var(--yellow);
        color: var(--primary-color);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .category-link:hover {
        background-color: var(--medium-gray);
        transform: translateY(-2px);
    }

    /* Enhanced Search Bar */
    .enhanced-search-bar {
        flex-grow: 1;
        max-width: 400px;
    }

    .search-form {
        display: flex;
        position: relative;
    }

    .search-form input {
        width: 100%;
        padding: 12px 20px;
        border-radius: 30px;
        border: 2px solid var(--light-gray);
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .search-form input:focus {
        border-color: var(--yellow);
        box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2);
        outline: none;
    }

    .search-button {
        position: absolute;
        right: 5px;
        top: 50%;
        transform: translateY(-50%);
        background-color: var(--yellow);
        color: var(--primary-color);
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .search-button:hover {
        background-color: var(--primary-color);
        color: var(--secondary-color);
    }

    /* Enhanced Product Grid */
    .enhanced-product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 30px;
    }

    .enhanced-product-card {
        background-color: var(--secondary-color);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        position: relative;
    }

    .enhanced-product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }

    .product-badge {
        position: absolute;
        top: 15px;
        left: 15px;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.7rem;
        font-weight: 700;
        text-transform: uppercase;
        color: white;
        z-index: 2;
        background-color: #ff6b6b;
    }

    .product-badge.bestseller {
        background-color: #ff6b6b;
    }

    .product-badge.seasonal {
        background-color: #51cf66;
    }

    .product-badge.limited_time {
        background-color: #fcc419;
    }

    .product-badge.new {
        background-color: #339af0;
    }

    .product-badge.sale {
        background-color: #f06595;
    }

    .product-image-container {
        position: relative;
        overflow: hidden;
        aspect-ratio: 1 / 1;
    }

    .product-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }

    .product-image-link:hover .product-img {
        transform: scale(1.05);
    }

    .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .product-image-link:hover .image-overlay {
        opacity: 1;
    }

    .view-details {
        color: white;
        background-color: var(--primary-color);
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .product-info {
        padding: 20px;
    }

    .product-title {
        font-size: 1.1rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .product-title a {
        color: var(--primary-color);
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .product-title a:hover {
        color: var(--yellow);
    }

    .product-size {
        color: var(--dark-gray);
        font-size: 0.9rem;
        margin-bottom: 10px;
    }

    .enhanced-quantity-selector {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        border: 1px solid var(--light-gray);
        border-radius: 30px;
        overflow: hidden;
        width: fit-content;
    }

    .quantity-btn {
        width: 36px;
        height: 36px;
        background-color: var(--light-gray);
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .quantity-btn:hover {
        background-color: var(--medium-gray);
    }

    .quantity-btn:active {
        transform: scale(0.95);
    }

    .quantity-input {
        width: 40px;
        height: 36px;
        border: none;
        text-align: center;
        font-size: 1rem;
        font-weight: 600;
        background-color: transparent;
    }

    .enhanced-add-to-cart {
        width: 100%;
        padding: 12px 0;
        background-color: var(--yellow);
        color: var(--primary-color);
        border: none;
        border-radius: 30px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .enhanced-add-to-cart:hover {
        background-color: var(--primary-color);
        color: var(--secondary-color);
    }

    .enhanced-add-to-cart:active {
        transform: scale(0.98);
    }

    /* Product List Variant Styles */
    .price-range .from-label {
        font-size: 0.8rem;
        color: var(--dark-gray);
        margin-right: 5px;
    }

    .variant-toggle-section {
        margin-bottom: 15px;
    }

    .variant-toggle-btn {
        width: 100%;
        padding: 8px 12px;
        background-color: var(--light-gray);
        border: 1px solid var(--medium-gray);
        border-radius: 20px;
        color: var(--primary-color);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .variant-toggle-btn:hover {
        background-color: var(--yellow);
        border-color: var(--primary-color);
    }

    .variant-toggle-btn .toggle-icon {
        transition: transform 0.3s ease;
    }

    .variant-toggle-btn.expanded .toggle-icon {
        transform: rotate(180deg);
    }

    .variant-options-container {
        margin-bottom: 15px;
        border: 1px solid var(--light-gray);
        border-radius: 10px;
        overflow: hidden;
        background-color: var(--cream);
    }

    .variant-option {
        padding: 12px;
        border-bottom: 1px solid var(--light-gray);
        transition: background-color 0.2s ease;
    }

    .variant-option:last-child {
        border-bottom: none;
    }

    .variant-option:hover {
        background-color: rgba(255, 193, 7, 0.1);
    }

    .variant-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .variant-name {
        font-weight: 600;
        color: var(--primary-color);
    }

    .variant-price {
        font-weight: 700;
        color: var(--primary-color);
    }

    .variant-cart-form {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .variant-cart-form .enhanced-quantity-selector {
        margin-bottom: 0;
        flex-shrink: 0;
    }

    .variant-add-to-cart {
        flex-grow: 1;
        padding: 8px 12px;
        background-color: var(--yellow);
        color: var(--primary-color);
        border: none;
        border-radius: 15px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
        font-size: 0.9rem;
    }

    .variant-add-to-cart:hover {
        background-color: var(--primary-color);
        color: var(--secondary-color);
    }

    /* Empty Products State */
    .empty-products-container {
        grid-column: 1 / -1;
        text-align: center;
        padding: 60px 20px;
        background-color: var(--secondary-color);
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .empty-products-icon {
        color: var(--medium-gray);
        margin-bottom: 20px;
    }

    .empty-products-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        color: var(--primary-color);
    }

    .empty-products-message {
        color: var(--dark-gray);
        margin-bottom: 30px;
        font-size: 1.1rem;
    }

    .return-to-shop {
        display: inline-block;
        padding: 12px 24px;
        background-color: var(--yellow);
        color: var(--primary-color);
        border-radius: 30px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .return-to-shop:hover {
        background-color: var(--primary-color);
        color: var(--secondary-color);
        transform: translateY(-2px);
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .product-list-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .enhanced-search-bar {
            width: 100%;
            max-width: none;
        }

        .enhanced-product-grid {
            grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
            gap: 20px;
        }
    }

    @media (max-width: 480px) {
        .enhanced-product-grid {
            grid-template-columns: 1fr;
        }

        .product-info {
            padding: 15px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize variant toggle functionality
    const variantToggleBtns = document.querySelectorAll('.variant-toggle-btn');

    variantToggleBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const productId = this.getAttribute('data-product-id');
            const variantsContainer = document.getElementById(`variants-${productId}`);
            const toggleIcon = this.querySelector('.toggle-icon');
            const toggleText = this.querySelector('.toggle-text');

            if (variantsContainer.style.display === 'none' || variantsContainer.style.display === '') {
                // Show variants
                variantsContainer.style.display = 'block';
                this.classList.add('expanded');
                toggleText.textContent = 'Hide Sizes';

                // Animate the container
                variantsContainer.style.opacity = '0';
                variantsContainer.style.transform = 'translateY(-10px)';
                setTimeout(() => {
                    variantsContainer.style.transition = 'all 0.3s ease';
                    variantsContainer.style.opacity = '1';
                    variantsContainer.style.transform = 'translateY(0)';
                }, 10);
            } else {
                // Hide variants
                variantsContainer.style.transition = 'all 0.3s ease';
                variantsContainer.style.opacity = '0';
                variantsContainer.style.transform = 'translateY(-10px)';

                setTimeout(() => {
                    variantsContainer.style.display = 'none';
                    this.classList.remove('expanded');
                    toggleText.textContent = 'View Sizes';
                }, 300);
            }
        });
    });

    // Initialize quantity selectors for all forms
    const quantitySelectors = document.querySelectorAll('.enhanced-quantity-selector');

    quantitySelectors.forEach(selector => {
        const minusBtn = selector.querySelector('.minus');
        const plusBtn = selector.querySelector('.plus');
        const quantityInput = selector.querySelector('.quantity-input');

        if (minusBtn && plusBtn && quantityInput) {
            minusBtn.addEventListener('click', function() {
                const currentValue = parseInt(quantityInput.value) || 1;
                if (currentValue > 1) {
                    quantityInput.value = currentValue - 1;
                    animateQuantityChange(quantityInput);
                }
            });

            plusBtn.addEventListener('click', function() {
                const currentValue = parseInt(quantityInput.value) || 1;
                quantityInput.value = currentValue + 1;
                animateQuantityChange(quantityInput);
            });

            // Ensure minimum value
            quantityInput.addEventListener('change', function() {
                const value = parseInt(this.value);
                if (isNaN(value) || value < 1) {
                    this.value = 1;
                }
            });
        }
    });

    function animateQuantityChange(input) {
        input.style.transform = 'scale(1.1)';
        input.style.backgroundColor = 'rgba(255, 193, 7, 0.2)';

        setTimeout(() => {
            input.style.transform = 'scale(1)';
            input.style.backgroundColor = '';
        }, 200);
    }

    // Add loading state to cart forms
    const cartForms = document.querySelectorAll('.cart-form, .variant-cart-form');

    cartForms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
            submitBtn.disabled = true;

            // Re-enable after 3 seconds (in case of slow response)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
        });
    });
});
</script>
{% endblock %}
